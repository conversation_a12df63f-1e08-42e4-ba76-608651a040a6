CREATE DEFINER=`root`@`%` PROCEDURE `rms_fx_etyy`(IN p_Code VARCHAR(50),
    IN p_akb020 VARCHAR(20),
    IN p_yp_code VARCHAR(20),
    IN p_csrq VARCHAR(20)
)
    COMMENT '儿童用药分析存储过程'
main_block: BEGIN
		DECLARE v_nl DECIMAL(18,2);
		DECLARE v_id VARCHAR(20);
		DECLARE v_sda_id VARCHAR(20);
		DECLARE v_ywa_name VARCHAR(50);
		DECLARE v_n_count INT DEFAULT 0;
		DECLARE v_age INT;
		
		-- 获取标准数据ID
		SELECT sda_id INTO v_sda_id 
		FROM rms_t_byyydzb 
		WHERE yp_code = p_yp_code
		LIMIT 1;
		
		-- 如果没有SDA ID则返回
		IF v_sda_id IS NULL OR v_sda_id = '' THEN
				LEAVE main_block;
		END IF;
		
		-- 检查是否为中药
		SELECT COUNT(1) INTO v_n_count 
		FROM rms_itf_hos_drug 
		WHERE DRUG_CODE = p_yp_code AND ZX_FLAG = '3';
		
		-- 如果是中药则返回
		IF v_n_count > 0 THEN
				LEAVE main_block;
		END IF;
		
		-- 获取药品名称
		SELECT DRUG_NAME INTO v_ywa_name 
		FROM rms_itf_hos_drug 
		WHERE DRUG_CODE = p_yp_code
		LIMIT 1;
		
		-- 计算年龄（将天数转换为年数）
		-- 支持多种日期格式：YYYY-MM-DD 或 YYYY-M-D 或 YYYYMMDD
		SET v_nl = CASE 
				WHEN p_csrq LIKE '%-%' THEN 
						CAST(DATEDIFF(CURDATE(), STR_TO_DATE(p_csrq, '%Y-%m-%d')) / 365.0 AS DECIMAL(18,2))
				WHEN LENGTH(p_csrq) = 8 THEN
						CAST(DATEDIFF(CURDATE(), STR_TO_DATE(p_csrq, '%Y%m%d')) / 365.0 AS DECIMAL(18,2))
				ELSE
						CAST(DATEDIFF(CURDATE(), STR_TO_DATE(p_csrq, '%Y-%m-%d')) / 365.0 AS DECIMAL(18,2))
		END;
		
		-- 如果年龄小于1岁
		IF CAST(v_nl AS DECIMAL(18,2)) < 1 THEN
				-- 找到最接近的年龄ID
				SELECT id INTO v_id 
				FROM rms_t_sda_age 
				ORDER BY ABS(v_nl - age)
				LIMIT 1;
				
				-- 插入儿童用药分析结果
				INSERT INTO rms_t_pres_fx (
						Code, ywa, ywb, wtlvlcode, wtlvl, wtcode, wtsp, wtname, title, detail, flag, text
				)
				SELECT 
						p_Code,
						v_ywa_name as ywa,
						'' as ywb,
						CASE WHEN a.result = '0' THEN '1' ELSE '1' END as wtlvlcode,
						(SELECT DISTINCT d.wtlvl FROM rms_wtlb d WHERE d.wtlvlcode = a.result) as wtlvl,
						CASE 
								WHEN a.result = '0' THEN 'RLT031' 
								WHEN a.result = '1' THEN 'RLT032' 
								WHEN a.result = '2' THEN 'RLT034' 
						END as wtcode,
						CASE 
								WHEN a.result = '0' THEN 'etjj' 
								WHEN a.result = '1' THEN 'etwt' 
								WHEN a.result = '2' THEN 'etjjts' 
						END as wtsp,
						CASE 
								WHEN a.result = '0' THEN '特殊人群禁用' 
								WHEN a.result = '1' THEN '特殊人群慎用' 
								WHEN a.result = '2' THEN '特殊人群提示' 
						END as wtname,
						'儿童用药' as title,
						CASE 
								WHEN a.result = '0' THEN CONCAT('说明书提示：', v_ywa_name, '儿童禁用！') 
								ELSE CONCAT('说明书提示：', v_ywa_name, '儿童慎用！') 
						END as detail,
						0 as flag,
						'儿童用药' as text
				FROM rms_t_sda_chd a
				WHERE a.sda_id = v_sda_id
				AND chd_jd_min <= CAST(v_id AS DECIMAL)
				AND chd_jd_max >= CAST(v_id AS DECIMAL)
				AND a.result <> '9';
				
		ELSE
				-- 年龄大于等于1岁
				SET v_age = CEILING(v_nl);
				
				-- 获取对应年龄的ID
				SELECT id INTO v_id 
				FROM rms_t_sda_age 
				WHERE age = v_age
				LIMIT 1;
				
				-- 插入儿童用药分析结果
				INSERT INTO rms_t_pres_fx (
						Code, ywa, ywb, wtlvlcode, wtlvl, wtcode, wtsp, wtname, title, detail, flag, text
				)
				SELECT 
						p_Code,
						v_ywa_name as ywa,
						'' as ywb,
						CASE WHEN a.result = '0' THEN '0' ELSE '1' END as wtlvlcode,
						(SELECT DISTINCT d.wtlvl FROM rms_wtlb d WHERE d.wtlvlcode = a.result) as wtlvl,
						CASE 
								WHEN a.result = '0' THEN 'RLT031' 
								WHEN a.result = '1' THEN 'RLT032' 
								WHEN a.result = '2' THEN 'RLT034' 
						END as wtcode,
						CASE 
								WHEN a.result = '0' THEN 'etjj' 
								WHEN a.result = '1' THEN 'etwt' 
								WHEN a.result = '2' THEN 'etjjts' 
						END as wtsp,
						CASE 
								WHEN a.result = '0' THEN '特殊人群禁用' 
								WHEN a.result = '1' THEN '特殊人群慎用' 
								WHEN a.result = '2' THEN '特殊人群提示' 
						END as wtname,
						'儿童用药' as title,
						CASE 
								WHEN a.result = '0' THEN CONCAT('说明书提示：', v_ywa_name, '儿童禁用！') 
								ELSE CONCAT('说明书提示：', v_ywa_name, '儿童慎用！') 
						END as detail,
						0 as flag,
						'儿童用药' as text
				FROM rms_t_sda_chd a
				WHERE a.sda_id = v_sda_id
				AND a.result <> '9'
				AND chd_jd_min <= CAST(v_id AS DECIMAL)
				AND chd_jd_max >= CAST(v_id AS DECIMAL);
		END IF;
END